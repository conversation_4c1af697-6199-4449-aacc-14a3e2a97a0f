<?php

declare(strict_types=1);

namespace App\DTO\Welding\ACST;

use App\DTO\DataTransferObject;
use Carbon\Carbon;

/**
 * <AUTHOR>
 */
class AcstShortDTO extends DataTransferObject
{
    /** @var int */
    public int $id;

    /** @var null|string */
    public ?string $certificate_number;

    /** @var null|string */
    public ?string $date_of_issue;

    /** @var null|string */
    public ?string $validity_date;

    /** @var null|int */
    public ?int $settings_attestation_id;

    /** @var null|string */
    public ?string $settings_attestation_code;

    /**
     * @param array $data
     *
     * @return AcstShortDTO
     */
    public static function createFromArray(array $data): self
    {
        return new self([
            'id' => $data['id'],
            'certificate_number' => $data['certificate_number'],
            'date_of_issue' => $data['date_of_issue']
                ? Carbon::parse($data['date_of_issue'])->toIso8601String()
                : null,
            'validity_date' => $data['validity_date']
                ? Carbon::parse($data['validity_date'])->toIso8601String()
                : null,
            'settings_attestation_id' => $data['settings_attestation_id'],
            'settings_attestation_code' => $data['settings_attestation_code'],
        ]);
    }
}
