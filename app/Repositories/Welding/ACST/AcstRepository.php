<?php

declare(strict_types=1);

namespace App\Repositories\Welding\ACST;

use App\DTO\Filters\AcstAcsoAcsmFilterModalDTO;
use App\DTO\PaginateDTO;
use App\Models\Welding\ACST\Acst;
use App\Repositories\BaseRepository;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;

/**
 * <AUTHOR>
 *
 * @method Acst|Builder query()
 */
class AcstRepository extends BaseRepository
{
    public function __construct()
    {
        $this->initializeModel(Acst::class);
    }

    /**
     * @param AcstAcsoAcsmFilterModalDTO $filterDTO
     * @param PaginateDTO $paginateDTO
     * @param int $companyId
     *
     * @return LengthAwarePaginator
     */
    public function getPaginatedForModalByCompanyIdAndFilter(
        AcstAcsoAcsmFilterModalDTO $filterDTO,
        PaginateDTO $paginateDTO,
        int $companyId,
    ): LengthAwarePaginator {
        return $this
            ->query()
            ->select([
                'welding_acst.id',
                'welding_acst.certificate_number',
                'welding_acst.date_of_issue',
                'welding_acst.validity_date',
                'welding_acst.settings_attestation_id',
                'settings_welding_technologies_attestations.code as settings_attestation_code',
            ])
            ->leftJoin(
                'settings_welding_technologies_attestations',
                'settings_welding_technologies_attestations.id',
                '=',
                'welding_acst.settings_attestation_id',
            )
            ->where('welding_acst.company_id', $companyId)
            ->when(
                $filterDTO->hasProperty('search') && $filterDTO->search !== null,
                static function (Builder $query) use ($filterDTO): Builder {
                    return $query->where(function (Builder $query) use ($filterDTO) {
                        $query
                            ->where('welding_acst.certificate_number', 'ilike', "%{$filterDTO->search}%")
                            ->orWhere('settings_welding_technologies_attestations.code', 'ilike', "%{$filterDTO->search}%");
                    });
                },
            )
            ->when(
                ! $filterDTO->hasProperty('show_archived') || $filterDTO->show_archived === false,
                static function (Builder $query): Builder {
                    return $query->where('welding_acst.validity_date', '>', now());
                },
            )
            ->orderBy('settings_welding_technologies_attestations.code')
            ->orderBy('welding_acst.certificate_number')
            ->orderBy('welding_acst.date_of_issue')
            ->pagination($paginateDTO);
    }
}
