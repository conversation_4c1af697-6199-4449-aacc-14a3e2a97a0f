<?php

declare(strict_types=1);

namespace Tests\Unit\Welding\ACST;

use App\DTO\Filters\AcstAcsoAcsmFilterModalDTO;
use App\DTO\PaginateDTO;
use App\DTO\Welding\ACST\AcstShortDTO;
use App\Models\Welding\ACST\Acst;
use App\Repositories\Welding\ACST\AcstRepository;
use App\Services\Document\DocumentService;
use App\Services\Welding\ACST\AcstService;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Mockery;
use Tests\TestCase;

/**
 * <AUTHOR> Test
 *
 * @internal
 * @small
 */
class AcstServiceTest extends TestCase
{
    /** @var AcstService */
    private AcstService $service;

    /** @var AcstRepository */
    private AcstRepository $acstRepositoryMock;

    /** @var DocumentService */
    private DocumentService $documentServiceMock;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();

        $this->acstRepositoryMock = Mockery::mock(AcstRepository::class);
        $this->documentServiceMock = Mockery::mock(DocumentService::class);

        $this->service = new AcstService(
            $this->documentServiceMock,
            $this->acstRepositoryMock
        );
    }

    /**
     * @return void
     */
    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * @return void
     */
    public function test_get_acst_for_modal_transforms_collection_to_acst_short_dto(): void
    {
        // Arrange
        $filterDTO = new AcstAcsoAcsmFilterModalDTO([
            'search' => 'test search',
            'show_archived' => false
        ]);
        
        $paginateDTO = new PaginateDTO([
            'page' => 1,
            'count' => 10
        ]);
        
        $companyId = 123;

        // Create mock Acst models
        $acstData1 = [
            'id' => 1,
            'certificate_number' => 'CERT-001',
            'date_of_issue' => '2023-01-15',
            'validity_date' => '2024-01-15',
            'settings_attestation_id' => 10,
            'settings_attestation_code' => 'CODE-001'
        ];

        $acstData2 = [
            'id' => 2,
            'certificate_number' => 'CERT-002',
            'date_of_issue' => '2023-02-20',
            'validity_date' => null,
            'settings_attestation_id' => null,
            'settings_attestation_code' => null
        ];

        $acstMock1 = Mockery::mock(Acst::class);
        $acstMock1->shouldReceive('toArray')->andReturn($acstData1);

        $acstMock2 = Mockery::mock(Acst::class);
        $acstMock2->shouldReceive('toArray')->andReturn($acstData2);

        $collection = new Collection([$acstMock1, $acstMock2]);

        // Create mock paginator
        $paginatorMock = Mockery::mock(LengthAwarePaginator::class);
        $paginatorMock->shouldReceive('getCollection')->andReturn($collection);

        // Mock repository method
        $this->acstRepositoryMock
            ->shouldReceive('getPaginatedForModalByCompanyIdAndFilter')
            ->once()
            ->with($filterDTO, $paginateDTO, $companyId)
            ->andReturn($paginatorMock);

        // Act
        $result = $this->service->getAcstForModal($filterDTO, $paginateDTO, $companyId);

        // Assert
        $this->assertInstanceOf(LengthAwarePaginator::class, $result);
        
        $transformedCollection = $result->getCollection();
        $this->assertCount(2, $transformedCollection);
        
        // Check first transformed item
        $firstItem = $transformedCollection->first();
        $this->assertInstanceOf(AcstShortDTO::class, $firstItem);
        $this->assertEquals(1, $firstItem->id);
        $this->assertEquals('CERT-001', $firstItem->certificate_number);
        $this->assertEquals('2023-01-15T00:00:00+00:00', $firstItem->date_of_issue);
        $this->assertEquals('2024-01-15T00:00:00+00:00', $firstItem->validity_date);
        $this->assertEquals(10, $firstItem->settings_attestation_id);
        $this->assertEquals('CODE-001', $firstItem->settings_attestation_code);
        
        // Check second transformed item
        $secondItem = $transformedCollection->last();
        $this->assertInstanceOf(AcstShortDTO::class, $secondItem);
        $this->assertEquals(2, $secondItem->id);
        $this->assertEquals('CERT-002', $secondItem->certificate_number);
        $this->assertEquals('2023-02-20T00:00:00+00:00', $secondItem->date_of_issue);
        $this->assertNull($secondItem->validity_date);
        $this->assertNull($secondItem->settings_attestation_id);
        $this->assertNull($secondItem->settings_attestation_code);
    }

    /**
     * @return void
     */
    public function test_get_acst_for_modal_handles_empty_collection(): void
    {
        // Arrange
        $filterDTO = new AcstAcsoAcsmFilterModalDTO([
            'search' => 'no results',
            'show_archived' => false
        ]);
        
        $paginateDTO = new PaginateDTO([
            'page' => 1,
            'count' => 10
        ]);
        
        $companyId = 456;

        $emptyCollection = new Collection([]);

        // Create mock paginator with empty collection
        $paginatorMock = Mockery::mock(LengthAwarePaginator::class);
        $paginatorMock->shouldReceive('getCollection')->andReturn($emptyCollection);

        // Mock repository method
        $this->acstRepositoryMock
            ->shouldReceive('getPaginatedForModalByCompanyIdAndFilter')
            ->once()
            ->with($filterDTO, $paginateDTO, $companyId)
            ->andReturn($paginatorMock);

        // Act
        $result = $this->service->getAcstForModal($filterDTO, $paginateDTO, $companyId);

        // Assert
        $this->assertInstanceOf(LengthAwarePaginator::class, $result);
        $this->assertCount(0, $result->getCollection());
    }
}
